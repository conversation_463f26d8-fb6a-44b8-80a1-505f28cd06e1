#!/usr/bin/env python3
"""
Virtual Environment Cleaner
Scans disk for venv/.venv directories and removes those not modified in the last month.
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime, timedelta
from dataclasses import dataclass
from typing import List, Tuple
from loguru import logger as log


@dataclass
class Config:
    """Configuration for venv cleaner."""
    # Scanning
    SCAN_ROOT: str = os.getenv("VENV_SCAN_ROOT", str(Path.home()))
    VENV_NAMES: List[str] = ["venv", ".venv", "env", ".env", "virtualenv"]
    
    # Age threshold
    MAX_AGE_DAYS: int = int(os.getenv("VENV_MAX_AGE_DAYS", "30"))
    
    # Safety
    DRY_RUN: bool = os.getenv("VENV_DRY_RUN", "true").lower() == "true"
    EXCLUDE_PATHS: List[str] = [
        "/System",
        "/Library",
        "/Applications",
        "/.Trash",
        "/node_modules",
        "/.git",
    ]
    
    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
    
    def log_init(self):
        """Initialize loguru with color formatting."""
        log.remove()
        log.add(sys.stderr, format=self.LOG_FORMAT, level=self.LOG_LEVEL)


CONFIG = Config()
CONFIG.log_init()


class VenvCleaner:
    """Finds and removes old virtual environments."""
    
    def __init__(self):
        self.found_venvs: List[Tuple[Path, float]] = []
        self.total_size = 0
        self.removed_count = 0
        self.removed_size = 0
        
    def _should_skip_path(self, path: Path) -> bool:
        """Check if path should be skipped based on exclusions."""
        path_str = str(path)
        return any(exclude in path_str for exclude in CONFIG.EXCLUDE_PATHS)
    
    def _get_dir_size(self, path: Path) -> float:
        """Calculate total size of directory in MB."""
        total = 0
        try:
            for entry in path.rglob("*"):
                if entry.is_file():
                    total += entry.stat().st_size
        except (PermissionError, OSError):
            pass
        return total / (1024 * 1024)  # Convert to MB
    
    def _get_last_modified(self, path: Path) -> datetime:
        """Get the most recent modification time in directory."""
        latest = datetime.fromtimestamp(path.stat().st_mtime)
        try:
            for entry in path.rglob("*"):
                mtime = datetime.fromtimestamp(entry.stat().st_mtime)
                if mtime > latest:
                    latest = mtime
        except (PermissionError, OSError):
            pass
        return latest
    
    def _is_venv(self, path: Path) -> bool:
        """Check if directory is likely a virtual environment."""
        # Check for common venv indicators
        indicators = [
            path / "bin" / "python",
            path / "Scripts" / "python.exe",
            path / "pyvenv.cfg",
            path / "bin" / "activate",
            path / "Scripts" / "activate.bat",
        ]
        return any(indicator.exists() for indicator in indicators)
    
    def scan(self) -> None:
        """Scan for virtual environments."""
        log.info(f"Scanning for virtual environments in: {CONFIG.SCAN_ROOT}")
        log.info(f"Looking for directories named: {', '.join(CONFIG.VENV_NAMES)}")
        
        scan_path = Path(CONFIG.SCAN_ROOT)
        cutoff_date = datetime.now() - timedelta(days=CONFIG.MAX_AGE_DAYS)
        
        for root, dirs, _ in os.walk(scan_path):
            root_path = Path(root)
            
            # Skip excluded paths
            if self._should_skip_path(root_path):
                dirs[:] = []  # Don't descend into this directory
                continue
            
            # Check each directory
            for dir_name in dirs[:]:  # Use slice to allow modification
                if dir_name in CONFIG.VENV_NAMES:
                    venv_path = root_path / dir_name
                    
                    # Verify it's actually a venv
                    if not self._is_venv(venv_path):
                        log.debug(f"Skipping {venv_path} - not a venv")
                        continue
                    
                    # Get size and age
                    size_mb = self._get_dir_size(venv_path)
                    last_modified = self._get_last_modified(venv_path)
                    age_days = (datetime.now() - last_modified).days
                    
                    self.found_venvs.append((venv_path, size_mb))
                    self.total_size += size_mb
                    
                    if last_modified < cutoff_date:
                        log.info(f"Found old venv: {venv_path} ({size_mb:.1f} MB, {age_days} days old)")
                    else:
                        log.debug(f"Found recent venv: {venv_path} ({size_mb:.1f} MB, {age_days} days old)")
                    
                    # Don't descend into venv directories
                    dirs.remove(dir_name)
    
    def clean(self) -> None:
        """Remove old virtual environments."""
        cutoff_date = datetime.now() - timedelta(days=CONFIG.MAX_AGE_DAYS)
        
        log.info(f"\nCleaning venvs older than {CONFIG.MAX_AGE_DAYS} days...")
        if CONFIG.DRY_RUN:
            log.warning("DRY RUN MODE - No files will be deleted")
        
        for venv_path, size_mb in self.found_venvs:
            last_modified = self._get_last_modified(venv_path)
            age_days = (datetime.now() - last_modified).days
            
            if last_modified < cutoff_date:
                if CONFIG.DRY_RUN:
                    log.info(f"Would remove: {venv_path} ({size_mb:.1f} MB)")
                else:
                    try:
                        shutil.rmtree(venv_path)
                        log.success(f"Removed: {venv_path} ({size_mb:.1f} MB)")
                        self.removed_count += 1
                        self.removed_size += size_mb
                    except Exception as e:
                        log.error(f"Failed to remove {venv_path}: {e}")
    
    def report(self) -> None:
        """Print summary report."""
        log.info("\n" + "="*60)
        log.info("SUMMARY")
        log.info("="*60)
        log.info(f"Total venvs found: {len(self.found_venvs)}")
        log.info(f"Total size: {self.total_size:.1f} MB ({self.total_size/1024:.2f} GB)")
        
        if CONFIG.DRY_RUN:
            removable = sum(1 for venv, _ in self.found_venvs 
                          if self._get_last_modified(venv) < datetime.now() - timedelta(days=CONFIG.MAX_AGE_DAYS))
            removable_size = sum(size for venv, size in self.found_venvs 
                               if self._get_last_modified(venv) < datetime.now() - timedelta(days=CONFIG.MAX_AGE_DAYS))
            log.info(f"Would remove: {removable} venvs")
            log.info(f"Would free: {removable_size:.1f} MB ({removable_size/1024:.2f} GB)")
        else:
            log.info(f"Removed: {self.removed_count} venvs")
            log.info(f"Freed: {self.removed_size:.1f} MB ({self.removed_size/1024:.2f} GB)")


def main():
    """Main entry point."""
    log.info("Virtual Environment Cleaner")
    log.info(f"Max age: {CONFIG.MAX_AGE_DAYS} days")
    log.info(f"Dry run: {CONFIG.DRY_RUN}")
    
    cleaner = VenvCleaner()
    
    try:
        cleaner.scan()
        cleaner.clean()
        cleaner.report()
    except KeyboardInterrupt:
        log.warning("\nOperation cancelled by user")
        sys.exit(1)
    except Exception as e:
        log.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 