<!-- Generated by SpecStory -->

# Untitled (2025-05-28 02:40:35)

_**User**_

Let's make /src/disk-space/clear_venvs.py

Use a python library to scan my whole disk and list all the venv / .venv dirs that are floating around taking up space and could likely be removed.

The util should remove any not modified in the last month.

Set config for which folder to scan

---

_**Assistant**_

I'll help you create a disk space utility to find and clean up old virtual environments. Let me start by setting up the project structure and implementing the script.

---

```bash
mkdir -p src/disk-space
```

```
Warning: You are not in a virtual environment!


```

---



---



---

